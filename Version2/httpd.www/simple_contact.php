<?php
/**
 * Simple Contact Form Handler
 * Direct form processing for Heart & Soul
 */

// Configuration
$to = '<EMAIL>';
$from = '<EMAIL>';
$website_name = 'Heart & Soul Medienproduktion';

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    // Get and sanitize form data
    $name = isset($_POST['name']) ? htmlspecialchars(trim($_POST['name'])) : '';
    $email = isset($_POST['email']) ? htmlspecialchars(trim($_POST['email'])) : '';
    $company = isset($_POST['company']) ? htmlspecialchars(trim($_POST['company'])) : '';
    $phone = isset($_POST['phone']) ? htmlspecialchars(trim($_POST['phone'])) : '';
    $subject = isset($_POST['subject']) ? htmlspecialchars(trim($_POST['subject'])) : '';
    $message = isset($_POST['message']) ? htmlspecialchars(trim($_POST['message'])) : '';

    $success = false;
    $error_message = '';

    // Basic validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error_message = 'Bitte füllen Sie alle Pflichtfelder aus.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Bitte geben Sie eine gültige E-Mail-Adresse ein.';
    } else {
        // Prepare email content
        $email_subject = 'Kontaktanfrage von ' . $name . ' - ' . $website_name;

        $email_body = "Neue Kontaktanfrage von der Heart & Soul Website\n";
        $email_body .= "==========================================\n\n";
        $email_body .= "Name: " . $name . "\n";
        $email_body .= "Firma: " . $company . "\n";
        $email_body .= "E-Mail: " . $email . "\n";
        $email_body .= "Telefon: " . $phone . "\n";
        $email_body .= "Betreff: " . $subject . "\n\n";
        $email_body .= "Nachricht:\n";
        $email_body .= "----------\n";
        $email_body .= $message . "\n\n";
        $email_body .= "Gesendet am: " . date('d.m.Y H:i:s') . "\n";
        $email_body .= "IP-Adresse: " . $_SERVER['REMOTE_ADDR'] . "\n";

        // Email headers
        $headers = "From: " . $website_name . " <" . $from . ">\r\n";
        $headers .= "Reply-To: " . $name . " <" . $email . ">\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

        // Send email
        if (mail($to, $email_subject, $email_body, $headers)) {
            $success = true;
        } else {
            $error_message = 'Fehler beim Senden der E-Mail. Bitte versuchen Sie es später erneut.';
        }
    }

    // Return response
    if (isset($_POST['ajax']) || !empty($_SERVER['HTTP_X_REQUESTED_WITH'])) {
        // AJAX request - return JSON
        header('Content-Type: application/json');
        echo json_encode(array(
            'success' => $success,
            'message' => $success ? 'Nachricht erfolgreich gesendet!' : $error_message
        ));
    } else {
        // Regular form submission - redirect or show message
        if ($success) {
            // Redirect to success page or show success message
            echo '<!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Nachricht gesendet - ' . $website_name . '</title>
                <style>
                    body { font-family: Arial, sans-serif; background: #121212; color: #f5f5f5; text-align: center; padding: 50px; }
                    .success { background: #1e1e1e; padding: 40px; border-radius: 10px; max-width: 500px; margin: 0 auto; }
                    .success h1 { color: #ff3030; margin-bottom: 20px; }
                    .btn { background: #ff3030; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 20px; }
                </style>
            </head>
            <body>
                <div class="success">
                    <h1>✓ Nachricht gesendet!</h1>
                    <p>Vielen Dank für Ihre Nachricht. Wir werden uns schnellstmöglich bei Ihnen melden.</p>
                    <a href="kontakt.html" class="btn">Zurück zum Kontakt</a>
                    <a href="index.html" class="btn">Zur Startseite</a>
                </div>
            </body>
            </html>';
        } else {
            // Show error message
            echo '<!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Fehler - ' . $website_name . '</title>
                <style>
                    body { font-family: Arial, sans-serif; background: #121212; color: #f5f5f5; text-align: center; padding: 50px; }
                    .error { background: #1e1e1e; padding: 40px; border-radius: 10px; max-width: 500px; margin: 0 auto; border: 1px solid #ff3030; }
                    .error h1 { color: #ff3030; margin-bottom: 20px; }
                    .btn { background: #ff3030; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 20px; }
                </style>
            </head>
            <body>
                <div class="error">
                    <h1>✗ Fehler beim Senden</h1>
                    <p>' . $error_message . '</p>
                    <a href="kontakt.html" class="btn">Zurück zum Kontakt</a>
                    <a href="mailto:<EMAIL>" class="btn">Direkt per E-Mail</a>
                </div>
            </body>
            </html>';
        }
    }
} else {
    // Not a POST request
    header('Location: kontakt.html');
    exit;
}
?>
