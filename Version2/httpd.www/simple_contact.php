<?php
/**
 * Simple Contact Form Handler
 * Fallback version for basic contact form functionality
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configuration
$to = '<EMAIL>';
$from = '<EMAIL>';

// Set content type header
header('Content-Type: application/json');

// Initialize response
$response = array('success' => false, 'message' => '');

try {
    // Check if it's a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }

    // Get form data
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $company = isset($_POST['company']) ? trim($_POST['company']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $subject = isset($_POST['subject']) ? trim($_POST['subject']) : '';
    $message = isset($_POST['message']) ? trim($_POST['message']) : '';

    // Basic validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        throw new Exception('Bitte füllen Sie alle Pflichtfelder aus.');
    }

    // Email validation
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Bitte geben Sie eine gültige E-Mail-Adresse ein.');
    }

    // Prepare email content
    $email_subject = 'Kontaktanfrage von ' . $name;
    $email_body = "Neue Kontaktanfrage von der Website:\n\n";
    $email_body .= "Name: " . $name . "\n";
    $email_body .= "Firma: " . $company . "\n";
    $email_body .= "E-Mail: " . $email . "\n";
    $email_body .= "Telefon: " . $phone . "\n";
    $email_body .= "Betreff: " . $subject . "\n\n";
    $email_body .= "Nachricht:\n" . $message . "\n\n";
    $email_body .= "Gesendet am: " . date('d.m.Y H:i:s') . "\n";

    // Email headers
    $headers = "From: " . $from . "\r\n";
    $headers .= "Reply-To: " . $email . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

    // Send email
    if (mail($to, $email_subject, $email_body, $headers)) {
        $response['success'] = true;
        $response['message'] = 'Nachricht erfolgreich gesendet!';
    } else {
        throw new Exception('Fehler beim Senden der E-Mail.');
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    error_log('Contact form error: ' . $e->getMessage());
}

// Return JSON response
echo json_encode($response);
?>
