<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kontaktformular Test - Heart & Soul</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #121212;
            color: #f5f5f5;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .form-container {
            background: #1e1e1e;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        h1 {
            color: #ff3030;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #2a2a2a;
            color: #f5f5f5;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        textarea {
            height: 120px;
            resize: vertical;
        }
        
        .btn {
            background: #ff3030;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            background: #e02020;
        }
        
        .test-info {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .mailto-link {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            background: #333;
            border-radius: 5px;
        }
        
        .mailto-link a {
            color: #ff3030;
            text-decoration: none;
            font-size: 18px;
        }
        
        .required {
            color: #ff3030;
        }
    </style>
</head>
<body>
    <h1>Kontaktformular Test</h1>
    
    <div class="test-info">
        <h3>Test-Information:</h3>
        <p>Dieses Formular sendet direkt an: <strong><EMAIL></strong></p>
        <p>Es funktioniert sowohl mit als auch ohne JavaScript.</p>
    </div>
    
    <div class="form-container">
        <h2>Kontakt aufnehmen</h2>
        
        <form action="/simple_contact.php" method="POST">
            <div class="form-group">
                <label for="name">Name <span class="required">*</span></label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="company">Firma</label>
                <input type="text" id="company" name="company">
            </div>
            
            <div class="form-group">
                <label for="email">E-Mail <span class="required">*</span></label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Telefon</label>
                <input type="tel" id="phone" name="phone">
            </div>
            
            <div class="form-group">
                <label for="subject">Betreff <span class="required">*</span></label>
                <input type="text" id="subject" name="subject" required>
            </div>
            
            <div class="form-group">
                <label for="message">Nachricht <span class="required">*</span></label>
                <textarea id="message" name="message" required placeholder="Ihre Nachricht an uns..."></textarea>
            </div>
            
            <button type="submit" class="btn">Nachricht senden</button>
        </form>
    </div>
    
    <div class="mailto-link">
        <h3>Alternative:</h3>
        <a href="mailto:<EMAIL>?subject=Kontaktanfrage&body=Hallo Heart %26 Soul Team,%0D%0A%0D%0AIch möchte gerne Kontakt aufnehmen.%0D%0A%0D%0AMit freundlichen Grüßen">
            📧 Direkt per E-Mail kontaktieren
        </a>
    </div>
    
    <div style="text-align: center; margin-top: 30px;">
        <a href="kontakt.html" style="color: #ff3030;">← Zurück zur normalen Kontaktseite</a>
    </div>
    
    <script>
        // Optional: AJAX enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('ajax', '1');
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Wird gesendet...';
            submitBtn.disabled = true;
            
            fetch('/simple_contact.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✓ Nachricht erfolgreich gesendet!\n\nWir melden uns schnellstmöglich bei Ihnen.');
                    this.reset();
                } else {
                    alert('✗ Fehler: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Fehler beim Senden. Bitte versuchen Sie es erneut oder nutzen Sie den direkten E-Mail-Link.');
            })
            .finally(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
