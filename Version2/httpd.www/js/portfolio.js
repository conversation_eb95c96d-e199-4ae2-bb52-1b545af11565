document.addEventListener('DOMContentLoaded', function() {
  console.log('Portfolio.js loaded');

  // Check if Vimeo iframes exist
  const vimeoIframes = document.querySelectorAll('.vimeo-container iframe');
  console.log('Found', vimeoIframes.length, 'Vimeo videos');

  // Simple intersection observer for lazy loading Vimeo videos
  const vimeoObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const iframe = entry.target;
        console.log('Vimeo video entering viewport:', iframe.src);

        // Vimeo iframes are already loaded, just log for debugging
        if (!iframe.dataset.observed) {
          iframe.dataset.observed = 'true';
          console.log('Vimeo iframe initialized:', iframe.title);
        }

        // Stop observing this iframe after first intersection
        vimeoObserver.unobserve(iframe);
      }
    });
  }, {
    rootMargin: '50px' // Start loading 50px before video enters viewport
  });

  // Observe all Vimeo iframes
  vimeoIframes.forEach(iframe => {
    vimeoObserver.observe(iframe);
  });

  // Handle any legacy video elements if they exist
  const portfolioVideos = document.querySelectorAll('.portfolio-video video');
  if (portfolioVideos.length > 0) {
    console.log('Found', portfolioVideos.length, 'legacy video elements');

    portfolioVideos.forEach((video, index) => {
      const source = video.querySelector('source');
      console.log(`Legacy video ${index + 1}:`, source ? source.src : 'no source');
    });
  }

  // Portfolio grid responsive behavior
  function handlePortfolioResize() {
    const portfolioGrid = document.querySelector('.portfolio-grid');
    if (portfolioGrid) {
      const items = portfolioGrid.querySelectorAll('.portfolio-item');
      console.log('Portfolio grid contains', items.length, 'items');
    }
  }

  // Call on load and resize
  handlePortfolioResize();
  window.addEventListener('resize', handlePortfolioResize);
});
