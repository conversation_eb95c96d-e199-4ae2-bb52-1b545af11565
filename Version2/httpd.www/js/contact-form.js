document.addEventListener('DOMContentLoaded', function() {
  console.log('Contact form script loaded');
  var contactForm = document.getElementById('contact-form');
  console.log('Contact form found:', contactForm);

  if (contactForm) {
    console.log('Adding submit event listener to contact form');
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();
      console.log('Contact form submitted');

      // Get form values
      var name = document.getElementById('name').value;
      var company = document.getElementById('company').value;
      var email = document.getElementById('email').value;
      var phone = document.getElementById('phone').value;
      var subject = document.getElementById('subject').value;
      var message = document.getElementById('message').value;
      var privacy = document.getElementById('privacy').checked;

      console.log('Form data:', { name: name, email: email, subject: subject, message: message, privacy: privacy });

      // Simple validation
      if (!name || !email || !subject || !message || !privacy) {
        console.log('Validation failed: missing required fields');
        alert('Bitte füllen Sie alle Pflichtfelder aus.');
        return;
      }

      // Email validation
      var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        console.log('Validation failed: invalid email');
        alert('Bitte geben Sie eine gültige E-Mail-Adresse ein.');
        return;
      }

      console.log('Validation passed, preparing to send email');

      // Disable the submit button and show loading state
      var submitButton = contactForm.querySelector('button[type="submit"]');
      var originalButtonText = submitButton.textContent;
      submitButton.disabled = true;
      submitButton.textContent = 'Wird gesendet...';

      // Collect form data
      var formData = {
        name: name,
        company: company,
        email: email,
        phone: phone,
        subject: subject,
        message: message,
        timestamp: new Date().toISOString(),
        processed: false
      };

      // Store in localStorage for admin dashboard
      var contactForms = JSON.parse(localStorage.getItem('contactForms')) || [];
      contactForms.push(formData);
      localStorage.setItem('contactForms', JSON.stringify(contactForms));

      // Send <NAME_EMAIL>
      var emailData = new FormData();
      emailData.append('name', name);
      emailData.append('company', company);
      emailData.append('email', email);
      emailData.append('phone', phone);
      emailData.append('subject', subject);
      emailData.append('message', message);

      console.log('Sending data to server...');

      // Send the data to the server
      fetch('/simple_contact.php', {
        method: 'POST',
        body: emailData
      })
      .then(function(response) {
        console.log('Server response status:', response.status);
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.status);
        }
        return response.json();
      })
      .then(function(data) {
        console.log('Server response data:', data);

        if (data.success) {
          console.log('Email sent successfully');

        // Create a success message
        var successMessage = document.createElement('div');
        successMessage.className = 'form-success-message';
        successMessage.innerHTML =
          '<div class="success-icon">' +
            '<i class="fas fa-check-circle"></i>' +
          '</div>' +
          '<h3>Vielen Dank für Ihre Nachricht!</h3>' +
          '<p>Wir haben Ihre Anfrage erhalten und werden uns schnellstmöglich bei Ihnen melden.</p>';

        // Replace the form with the success message
        contactForm.style.opacity = '0';
        setTimeout(function() {
          contactForm.parentNode.replaceChild(successMessage, contactForm);
          successMessage.style.opacity = '0';
          setTimeout(function() {
            successMessage.style.opacity = '1';
          }, 50);
        }, 300);
        } else {
          console.log('Server returned error:', data.message);
          throw new Error(data.message || 'Server error');
        }
      })
      .catch(error => {
        console.error('Error sending email:', error);

        // Reset button state
        submitButton.disabled = false;
        submitButton.textContent = originalButtonText;

        // Show error message
        const errorMessage = document.createElement('div');
        errorMessage.className = 'form-error-message';
        errorMessage.style.cssText = `
          background-color: #fee;
          border: 1px solid #fcc;
          color: #c33;
          padding: 15px;
          border-radius: 5px;
          margin-top: 15px;
          text-align: center;
        `;
        errorMessage.innerHTML = `
          <div style="margin-bottom: 10px;">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h4 style="margin: 0 0 10px 0;">Fehler beim Senden</h4>
          <p style="margin: 0;">Es gab ein Problem beim Senden Ihrer Nachricht. Bitte versuchen Sie es erneut oder kontaktieren Sie uns direkt unter <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
        `;

        // Remove any existing error messages
        const existingError = contactForm.querySelector('.form-error-message');
        if (existingError) {
          existingError.remove();
        }

        // Add error message to form
        contactForm.appendChild(errorMessage);

        // Add mailto fallback button
        const mailtoButton = document.createElement('button');
        mailtoButton.type = 'button';
        mailtoButton.className = 'btn btn-outline';
        mailtoButton.style.marginTop = '10px';
        mailtoButton.innerHTML = '<i class="fas fa-envelope"></i> E-Mail direkt senden';
        mailtoButton.onclick = function() {
          const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(
            `Name: ${name}\n` +
            `Firma: ${company}\n` +
            `E-Mail: ${email}\n` +
            `Telefon: ${phone}\n\n` +
            `Nachricht:\n${message}`
          )}`;
          window.location.href = mailtoLink;
        };
        errorMessage.appendChild(mailtoButton);

        // Scroll to error message
        errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
      });
    });
  }
});
